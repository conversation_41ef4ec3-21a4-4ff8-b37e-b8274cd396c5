# Pixel-Perfect Scaling para ICO

## Visão Geral

O ZapFormat implementa escalonamento pixel-perfect para conversões ICO, replicando o comportamento do convertico.com original. Esta funcionalidade é especialmente importante para pixel art e ícones pequenos.

## Como Funciona

### Detecção Automática de Tamanho

A função `calculatePixelPerfectScale()` analisa as dimensões da imagem original e determina o fator de escala ideal:

```typescript
// Imagens muito pequenas (≤16px) - Escala 8x ou 16x
8x8 pixels   → 128x128 pixels (16x scale)
16x16 pixels → 128x128 pixels (8x scale)

// Imagens pequenas (17-32px) - Escala 4x
32x32 pixels → 128x128 pixels (4x scale)

// Imagens médias (33-64px) - Escala 2x
64x64 pixels → 128x128 pixels (2x scale)

// Imagens grandes (>64px) - Sem escala
128x128 pixels → 128x128 pixels (1x scale)
```

### Configuração Canvas Pixel-Perfect

A função `createPixelPerfectCanvas()` configura o canvas para renderização sem suavização:

```typescript
// Desabilita suavização em todos os navegadores
ctx.imageSmoothingEnabled = false;
ctx.webkitImageSmoothingEnabled = false;
ctx.mozImageSmoothingEnabled = false;
ctx.msImageSmoothingEnabled = false;
```

### Algoritmo Nearest-Neighbor

O escalonamento usa o algoritmo nearest-neighbor através do `ctx.drawImage()`:

```typescript
ctx.drawImage(
  img,                    // Imagem fonte
  0, 0,                   // Posição fonte
  img.width, img.height,  // Dimensões fonte
  0, 0,                   // Posição destino
  targetWidth, targetHeight // Dimensões destino
);
```

## Exemplos de Uso

### Conversão Automática ICO

```typescript
// Ao selecionar formato ICO, o escalonamento é aplicado automaticamente
const convertedBlob = await convertImageToFormat(file, 'ico');
```

### Demonstração Pixel-Perfect

```typescript
// Para testar diferentes escalas
const scaledBlob = await createPixelPerfectDemo(file, 4); // 4x scale
```

## Comparação Visual

### Antes (Suavizado)
- Bordas borradas
- Perda de detalhes pixelizados
- Aparência "embaçada"

### Depois (Pixel-Perfect)
- Bordas nítidas e blocadas
- Preservação da estrutura pixelizada
- Aparência crisp e definida

## Casos de Uso Ideais

1. **Pixel Art**: Sprites de jogos, arte 8-bit/16-bit
2. **Ícones Pequenos**: Favicons, ícones de aplicativo
3. **Logos Simples**: Logotipos com poucos detalhes
4. **Gráficos Retro**: Arte no estilo retrô/vintage

## Configurações Técnicas

### Formatos Suportados
- **Entrada**: Qualquer formato de imagem suportado
- **Saída**: PNG (para ICO) com qualidade máxima

### Limitações
- Funciona apenas para conversões ICO
- Otimizado para imagens pequenas (≤64px)
- Não adequado para fotografias ou imagens complexas

### Performance
- Processamento rápido para imagens pequenas
- Uso eficiente de memória
- Sem bloqueio da UI

## Implementação

A funcionalidade está implementada em:
- `src/utils/imageProcessing.ts` - Lógica principal
- `src/utils/pixelArtUtils.ts` - Utilitários auxiliares

### Funções Principais

1. `calculatePixelPerfectScale()` - Calcula escala ideal
2. `createPixelPerfectCanvas()` - Cria canvas otimizado
3. `convertImageToFormat()` - Conversão principal
4. `createPixelPerfectDemo()` - Função de demonstração

## Testes

Para testar a funcionalidade:

1. Faça upload de uma imagem pequena (ex: 16x16px)
2. Selecione formato ICO
3. Converta e compare com o original
4. Observe a preservação da estrutura pixelizada

## Compatibilidade

- ✅ Chrome/Chromium
- ✅ Firefox
- ✅ Safari
- ✅ Edge
- ✅ Dispositivos móveis

A implementação usa APIs padrão do Canvas que são amplamente suportadas.
