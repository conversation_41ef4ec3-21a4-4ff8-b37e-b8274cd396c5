import { useState, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircle, Settings, Info } from 'lucide-react';
import { cn } from '../utils/cn';

interface FormatOption {
  id: string;
  icon: string;
}

export interface ConversionConfig {
  ico?: {
    maxSize: number;
    selectedSizes?: number[];
  };
  webp?: {
    quality: number;
  };
  svg?: {
    preset: string;
    detail: number;
    smoothing: number;
  };
}

interface ConversionOptionsProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
  config: ConversionConfig;
  onConfigChange: (config: ConversionConfig) => void;
  files?: File[];
}

export default function ConversionOptions({
  selectedFormat,
  onFormatChange,
  config,
  onConfigChange,
  files = []
}: ConversionOptionsProps) {
  const { t } = useTranslation();
  const [estimatedSize, setEstimatedSize] = useState<string>('');

  const formats = useMemo<FormatOption[]>(() => [
    { id: 'ico', icon: '🖼️' },
    { id: 'png', icon: '🎨' },
    { id: 'jpg', icon: '📷' },
    { id: 'webp', icon: '🌐' },
    { id: 'svg', icon: '✨' },
  ], []);

  const icoSizes = [16, 32, 48, 64, 128, 256];

  const getIncludedIcoSizes = (maxSize: number, selectedSizes?: number[]) => {
    if (selectedSizes && selectedSizes.length > 0) {
      // Use user-selected sizes
      return selectedSizes.sort((a, b) => a - b);
    }
    // Fallback: generate sizes up to maxSize (no forced base sizes)
    return icoSizes.filter(size => size <= maxSize).sort((a, b) => a - b);
  };

  // Estimate WebP file size based on quality
  const estimateWebPSize = (originalSize: number, quality: number) => {
    // Rough estimation: WebP typically reduces size by 25-35% at 90% quality
    const compressionFactor = 0.3 + (quality / 100) * 0.4; // 0.3 to 0.7
    return Math.round(originalSize * compressionFactor);
  };

  // Update estimated size when WebP quality changes
  useEffect(() => {
    if (selectedFormat === 'webp' && files.length > 0) {
      const totalOriginalSize = files.reduce((sum, file) => sum + file.size, 0);
      const quality = config.webp?.quality || 90;
      const estimatedTotalSize = estimateWebPSize(totalOriginalSize, quality);

      const formatSize = (bytes: number) => {
        if (bytes < 1024) return `${bytes} B`;
        if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
        return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
      };

      setEstimatedSize(`${formatSize(totalOriginalSize)} → ${formatSize(estimatedTotalSize)}`);
    } else {
      setEstimatedSize('');
    }
  }, [selectedFormat, config.webp?.quality, files]);

  const updateIcoConfig = (maxSize: number) => {
    onConfigChange({
      ...config,
      ico: {
        maxSize,
        selectedSizes: config.ico?.selectedSizes // Preserve selected sizes
      }
    });
  };

  const toggleIcoSize = (size: number) => {
    const currentSizes = config.ico?.selectedSizes || [];
    const isSelected = currentSizes.includes(size);

    let newSizes: number[];
    if (isSelected) {
      // Remove this size and all larger sizes
      newSizes = currentSizes.filter(s => s < size);
    } else {
      // Add this size and all smaller sizes
      const allSmallerSizes = icoSizes.filter(s => s <= size);
      newSizes = [...new Set([...currentSizes, ...allSmallerSizes])].sort((a, b) => a - b);
    }

    onConfigChange({
      ...config,
      ico: {
        maxSize: config.ico?.maxSize || 48,
        selectedSizes: newSizes
      }
    });
  };

  const clearIcoSizes = () => {
    onConfigChange({
      ...config,
      ico: {
        maxSize: config.ico?.maxSize || 48,
        selectedSizes: []
      }
    });
  };

  const updateWebPConfig = (quality: number) => {
    onConfigChange({
      ...config,
      webp: { quality }
    });
  };

  const updateSvgConfig = (updates: Partial<{ preset: string; detail: number; smoothing: number }>) => {
    onConfigChange({
      ...config,
      svg: {
        preset: 'default',
        detail: 50,
        smoothing: 50,
        ...config.svg,
        ...updates
      }
    });
  };

  return (
    <div>
      <h3 className="text-base font-semibold text-primary-900 mb-3 text-center">
        {t('converter.selectFormat')}
      </h3>

      {/* Layout horizontal: formatos à esquerda, configurações à direita */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Coluna da esquerda: Seleção de formatos */}
        <div>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-2">
            {formats.map((format) => (
              <button
                key={format.id}
                className={cn(
                  "relative flex flex-col items-center justify-center p-2.5 rounded-lg border-2 transition-all duration-300 hover:scale-105",
                  selectedFormat === format.id
                    ? "border-accent-500 bg-accent-500/10 shadow-lg"
                    : "border-dark-border bg-dark-surface hover:border-accent-500 hover:bg-accent-500/5 hover:shadow-md"
                )}
                onClick={() => onFormatChange(format.id)}
              >
                <span className="text-xl mb-1">{format.icon}</span>
                <span className="text-xs font-semibold text-primary-800">
                  {t(`formats.${format.id}`)}
                </span>

                {selectedFormat === format.id && (
                  <div className="absolute -top-1 -right-1">
                    <CheckCircle className="w-3.5 h-3.5 text-accent-500 fill-dark-surface shadow-lg rounded-full" />
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Coluna da direita: Configurações avançadas */}
        <div className="min-h-[160px]">
          {(selectedFormat === 'ico' || selectedFormat === 'webp' || selectedFormat === 'svg') ? (
            <div className="h-full">
              {renderAdvancedSettings()}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full p-4 bg-dark-surface/30 rounded-lg border border-dark-border/50">
              <div className="text-center">
                <Settings className="w-6 h-6 text-primary-500 mx-auto mb-1" />
                <p className="text-xs text-primary-600">
                  Selecione ICO, WebP ou SVG para ver configurações avançadas
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  function renderAdvancedSettings() {
    // Advanced Configuration for ICO
    if (selectedFormat === 'ico') {
      return (
        <div className="p-3 bg-dark-surface/50 rounded-lg border border-dark-border animate-fade-in h-full">
          <div className="flex items-center gap-2 mb-3">
            <Settings className="w-3.5 h-3.5 text-accent-500" />
            <h4 className="text-sm font-semibold text-primary-900">{t('converter.icoSettings')}</h4>
          </div>

          <div className="space-y-3">
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-xs font-medium text-primary-700">
                  Tamanhos de Ícone
                </label>
                <button
                  onClick={clearIcoSizes}
                  className="text-xs text-primary-600 hover:text-accent-400 transition-colors"
                >
                  Limpar
                </button>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-1.5">
                {icoSizes.map((size) => {
                  const isSelected = config.ico?.selectedSizes?.includes(size) || false;
                  return (
                    <button
                      key={size}
                      className={cn(
                        "px-2 py-1.5 text-xs rounded-md border transition-all duration-200",
                        isSelected
                          ? "border-accent-500 bg-accent-500/10 text-accent-400"
                          : "border-dark-border bg-dark-surface text-primary-700 hover:border-accent-500/50"
                      )}
                      onClick={() => toggleIcoSize(size)}
                    >
                      {size}×{size}
                    </button>
                  );
                })}
              </div>
              <p className="text-xs text-primary-600 mt-1.5">
                Clique para incluir tamanho + menores. Padrão: até 48×48.
              </p>
            </div>

            <div className="bg-dark-bg/50 rounded-md p-2.5">
              <div className="flex items-center gap-2 mb-1.5">
                <Info className="w-3 h-3 text-accent-500" />
                <span className="text-xs font-medium text-primary-800">Tamanhos incluídos</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {getIncludedIcoSizes(config.ico?.maxSize || 48, config.ico?.selectedSizes).map((size) => (
                  <span
                    key={size}
                    className="px-1.5 py-0.5 text-xs bg-accent-500/10 text-accent-400 rounded border border-accent-500/20"
                  >
                    {size}×{size}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Advanced Configuration for WebP
    if (selectedFormat === 'webp') {
      return (
        <div className="p-3 bg-dark-surface/50 rounded-lg border border-dark-border animate-fade-in h-full">
          <div className="flex items-center gap-2 mb-3">
            <Settings className="w-3.5 h-3.5 text-accent-500" />
            <h4 className="text-sm font-semibold text-primary-900">{t('converter.webpSettings')}</h4>
          </div>

          <div className="space-y-3">
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-xs font-medium text-primary-700">
                  {t('converter.quality')}
                </label>
                <span className="text-xs font-mono text-accent-400">
                  {config.webp?.quality || 90}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={config.webp?.quality || 90}
                onChange={(e) => updateWebPConfig(parseInt(e.target.value))}
                className="w-full h-2 bg-dark-border rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-primary-600 mt-1">
                <span>{t('converter.lowerSize')}</span>
                <span>{t('converter.betterQuality')}</span>
              </div>
            </div>

            {estimatedSize && (
              <div className="bg-dark-bg/50 rounded-md p-2.5">
                <div className="flex items-center gap-2 mb-1">
                  <Info className="w-3 h-3 text-accent-500" />
                  <span className="text-xs font-medium text-primary-800">{t('converter.sizeEstimate')}</span>
                </div>
                <p className="text-sm font-mono text-accent-400">{estimatedSize}</p>
                <p className="text-xs text-primary-600 mt-1">
                  {t('converter.sizeEstimateNote')}
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }

    // Advanced Configuration for SVG
    if (selectedFormat === 'svg') {
      return (
        <div className="p-3 bg-dark-surface/50 rounded-lg border border-dark-border animate-fade-in h-full">
          <div className="flex items-center gap-2 mb-3">
            <Settings className="w-3.5 h-3.5 text-accent-500" />
            <h4 className="text-sm font-semibold text-primary-900">Configurações SVG</h4>
          </div>

          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-primary-700 mb-2">
                Preset de Vetorização
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-1.5">
                {['default', 'posterized2', 'detailed', 'curvy'].map((preset) => (
                  <button
                    key={preset}
                    className={cn(
                      "px-2 py-1.5 text-xs rounded-md border transition-all duration-200 capitalize",
                      (config.svg?.preset || 'default') === preset
                        ? "border-accent-500 bg-accent-500/10 text-accent-400"
                        : "border-dark-border bg-dark-surface text-primary-700 hover:border-accent-500/50"
                    )}
                    onClick={() => updateSvgConfig({ preset })}
                  >
                    {preset === 'default' ? 'Padrão' :
                     preset === 'posterized2' ? 'Simples' :
                     preset === 'detailed' ? 'Detalhado' :
                     preset === 'curvy' ? 'Suave' : preset}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-xs font-medium text-primary-700">
                  Nível de Detalhe
                </label>
                <span className="text-xs font-mono text-accent-400">
                  {config.svg?.detail || 50}%
                </span>
              </div>
              <input
                type="range"
                min="10"
                max="100"
                value={config.svg?.detail || 50}
                onChange={(e) => updateSvgConfig({ detail: parseInt(e.target.value) })}
                className="w-full h-2 bg-dark-border rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-primary-600 mt-1">
                <span>Menos</span>
                <span>Mais</span>
              </div>
            </div>

            <div className="bg-dark-bg/50 rounded-md p-2.5">
              <div className="flex items-center gap-2 mb-1">
                <Info className="w-3 h-3 text-accent-500" />
                <span className="text-xs font-medium text-primary-800">Sobre SVG</span>
              </div>
              <p className="text-xs text-primary-600">
                Converte imagens em gráficos vetoriais escaláveis. Melhor para ícones e ilustrações.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return null;
  }
}