import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Menu, X, Globe } from 'lucide-react';
import { cn } from '../utils/cn';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { t, i18n } = useTranslation();
  
  const toggleLanguage = () => {
    const newLang = i18n.language === 'pt' ? 'en' : 'pt';
    i18n.changeLanguage(newLang);
  };
  
  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  return (
    <header className="bg-white/80 backdrop-blur-md border-b border-primary-100 sticky top-0 z-50">
      <div className="container-custom">
        <div className="flex justify-between items-center py-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-accent-500 to-accent-600 rounded-xl flex items-center justify-center shadow-lg">
              <svg
                viewBox="0 0 24 24"
                className="w-6 h-6 text-white"
                fill="none"
                stroke="currentColor"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M21.9 8.5a2.8 2.8 0 0 0-2.03-2.65L7.13 2.1a2.8 2.8 0 0 0-3.53 1.65L2.1 7.13A2.8 2.8 0 0 0 3.75 10.7L5 11.27V19a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-7.73l1.24-.57A2.8 2.8 0 0 0 21.9 8.5z"/>
                <path d="M7 16h.01"/>
                <path d="M17 16h.01"/>
                <path d="M11.25 12h1.5L14 16h-4l1.25-4z"/>
              </svg>
            </div>
            <span className="font-bold text-2xl text-primary-900 tracking-tight">ZapFormat</span>
          </div>
          
          <nav className="hidden md:flex space-x-8">
            <a href="#" className="text-primary-600 hover:text-accent-600 font-medium transition-colors">{t('header.home')}</a>
            <a href="#" className="text-primary-600 hover:text-accent-600 font-medium transition-colors">{t('header.about')}</a>
            <a href="#" className="text-primary-600 hover:text-accent-600 font-medium transition-colors">{t('header.contact')}</a>
            <a href="#" className="text-primary-600 hover:text-accent-600 font-medium transition-colors">{t('header.faq')}</a>
          </nav>

          <div className="flex items-center space-x-3">
            <button
              onClick={toggleLanguage}
              className="p-3 rounded-xl hover:bg-primary-50 transition-colors"
              aria-label="Toggle language"
            >
              <Globe className="w-5 h-5 text-primary-600" />
            </button>

            <button
              onClick={toggleMenu}
              className="md:hidden p-3 rounded-xl hover:bg-primary-50 transition-colors"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <X className="w-5 h-5 text-primary-600" /> : <Menu className="w-5 h-5 text-primary-600" />}
            </button>
          </div>
        </div>
      </div>
      
      {/* Mobile menu */}
      <div
        className={cn(
          "md:hidden absolute w-full bg-white/95 backdrop-blur-md border-b border-primary-100 shadow-lg transition-all duration-300 ease-in-out",
          isMenuOpen ? "max-h-64 opacity-100" : "max-h-0 opacity-0 invisible"
        )}
      >
        <div className="container-custom py-6 space-y-4">
          <a href="#" className="block text-primary-600 hover:text-accent-600 font-medium transition-colors">{t('header.home')}</a>
          <a href="#" className="block text-primary-600 hover:text-accent-600 font-medium transition-colors">{t('header.about')}</a>
          <a href="#" className="block text-primary-600 hover:text-accent-600 font-medium transition-colors">{t('header.contact')}</a>
          <a href="#" className="block text-primary-600 hover:text-accent-600 font-medium transition-colors">{t('header.faq')}</a>
        </div>
      </div>
    </header>
  );
}