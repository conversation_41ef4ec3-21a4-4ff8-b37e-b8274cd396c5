import { useEffect } from 'react';
import Converter from './components/Converter';
import './i18n';

function App() {
  useEffect(() => {
    document.title = 'ICOnverter - Conversor de Imagens Online';
  }, []);

  return (
    <div className="min-h-screen bg-dark-bg flex items-center justify-center p-4">
      <main className="w-full max-w-6xl">
        <Converter />
      </main>
    </div>
  );
}

export default App;