// Supported image formats with their MIME types and extensions
export const SUPPORTED_FORMATS = {
  // Common formats
  png: { mimeTypes: ['image/png'], extensions: ['.png'] },
  jpg: { mimeTypes: ['image/jpeg', 'image/jpg'], extensions: ['.jpg', '.jpeg'] },
  webp: { mimeTypes: ['image/webp'], extensions: ['.webp'] },
  svg: { mimeTypes: ['image/svg+xml'], extensions: ['.svg'] },
  ico: { mimeTypes: ['image/x-icon', 'image/vnd.microsoft.icon'], extensions: ['.ico'] },

  // Extended formats
  avif: { mimeTypes: ['image/avif'], extensions: ['.avif'] },
  heic: { mimeTypes: ['image/heic', 'image/heif'], extensions: ['.heic', '.heif'] },

  // RAW formats (note: browsers may not recognize these MIME types)
  raw: {
    mimeTypes: ['image/x-canon-cr2', 'image/x-nikon-nef', 'image/x-sony-arw', 'application/octet-stream'],
    extensions: ['.raw', '.cr2', '.nef', '.arw']
  },
};

// Maximum file size (50MB)
export const MAX_FILE_SIZE = 50 * 1024 * 1024;

// Get all supported extensions
export const getAllSupportedExtensions = (): string[] => {
  return Object.values(SUPPORTED_FORMATS).flatMap(format => format.extensions);
};

// Get all supported MIME types
export const getAllSupportedMimeTypes = (): string[] => {
  return Object.values(SUPPORTED_FORMATS).flatMap(format => format.mimeTypes);
};

// Validate file extension
export const isValidExtension = (fileName: string): boolean => {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return getAllSupportedExtensions().includes(extension);
};

// Validate file MIME type
export const isValidMimeType = (mimeType: string): boolean => {
  return getAllSupportedMimeTypes().includes(mimeType) || mimeType.startsWith('image/');
};

// Validate file size
export const isValidFileSize = (fileSize: number): boolean => {
  return fileSize <= MAX_FILE_SIZE;
};

// Comprehensive file validation
export const validateImageFile = (file: File): { isValid: boolean; error?: string } => {
  // Check file size
  if (!isValidFileSize(file.size)) {
    return {
      isValid: false,
      error: `Arquivo muito grande. Tamanho máximo: ${Math.round(MAX_FILE_SIZE / (1024 * 1024))}MB`
    };
  }

  // Check extension
  if (!isValidExtension(file.name)) {
    return {
      isValid: false,
      error: 'Formato de arquivo não suportado. Verifique a extensão do arquivo.'
    };
  }

  // Check MIME type (more lenient for RAW files)
  if (!isValidMimeType(file.type)) {
    // For RAW files, the browser might not recognize the MIME type
    const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    const rawExtensions = SUPPORTED_FORMATS.raw.extensions;
    
    if (!rawExtensions.includes(extension)) {
      return {
        isValid: false,
        error: 'Tipo de arquivo não reconhecido. Verifique se é uma imagem válida.'
      };
    }
  }

  return { isValid: true };
};

// Get format info by extension
export const getFormatByExtension = (fileName: string): string | null => {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  
  for (const [formatId, formatInfo] of Object.entries(SUPPORTED_FORMATS)) {
    if (formatInfo.extensions.includes(extension)) {
      return formatId;
    }
  }
  
  return null;
};

// Check if file is likely a RAW format
export const isRawFormat = (fileName: string): boolean => {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return SUPPORTED_FORMATS.raw.extensions.includes(extension);
};

// Get human-readable file size
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
